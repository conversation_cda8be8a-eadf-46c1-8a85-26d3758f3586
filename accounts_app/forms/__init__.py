"""
Forms package for accounts_app.

This package contains all form classes organized by feature area for better maintainability.
All forms are imported here to maintain backward compatibility.
"""

# --- Local App Imports ---
from .common import AccountDeactivationForm, AccessibleFormMixin
from .customer import (
    # Authentication forms
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerSignupForm,
    # Authentication forms have been migrated to Django allauth.
    # Only import business-related customer forms here.
    CustomerProfileForm,
)
from .provider import (
    # Provider auth forms migrated to allauth.
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
    ServiceProviderPasswordChangeForm,
)
from .team import TeamMemberForm


# Make all forms available at package level for backward compatibility
__all__ = [
    # Common forms and mixins
    'AccessibleFormMixin',
    'AccountDeactivationForm',
    
    # Customer forms
    'CustomerProfileForm',
    'CustomerLoginForm',
    'CustomerPasswordChangeForm',
    'CustomerSignupForm',
    
    # Service provider forms
    'ServiceProviderProfileForm',
    'ServiceProviderSignupForm',
    'ServiceProviderPasswordChangeForm',
    
    # Team management forms
    'TeamMemberForm',
]
