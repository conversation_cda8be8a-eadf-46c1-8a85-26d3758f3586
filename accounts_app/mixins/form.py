"""
Form Validation Mixins

This module provides reusable mixins for common form validation patterns
across the accounts_app. These mixins promote DRY principles and ensure
consistent validation behavior.
"""

import re
from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _

from ..forms.errors import get_error_message, get_success_message
from ..models import CustomUser


class EmailValidationMixin:
    """
    Mixin for email field validation with uniqueness checking.
    
    Provides methods for validating email format, uniqueness, and common
    email-related validation patterns.
    """
    
    def clean_email(self):
        """
        Clean and validate email field with uniqueness check.
        
        Returns:
            str: Cleaned email address
            
        Raises:
            ValidationError: If email is invalid or already exists
        """
        email = self.cleaned_data.get('email')
        
        if not email:
            return email
            
        # Convert to lowercase for consistency
        email = email.lower().strip()
        
        # Check maximum length
        if len(email) > 254:
            raise ValidationError(
                get_error_message('email', 'email_too_long'),
                code='email_too_long'
            )
        
        # Check for uniqueness (skip if editing existing user)
        if self._should_check_email_uniqueness(email):
            if CustomUser.objects.filter(email=email).exists():
                raise ValidationError(
                    get_error_message('email', 'email_exists'),
                    code='email_exists'
                )
        
        return email
    
    def _should_check_email_uniqueness(self, email):
        """
        Determine if email uniqueness should be checked.
        
        Args:
            email (str): Email to check
            
        Returns:
            bool: True if uniqueness should be checked
        """
        # Skip uniqueness check if editing existing user with same email
        if hasattr(self, 'instance') and self.instance and hasattr(self.instance, 'email'):
            return self.instance.email != email
        return True
    
    def clean_confirm_email(self):
        """
        Validate email confirmation field.
        
        Returns:
            str: Cleaned email address
            
        Raises:
            ValidationError: If emails don't match
        """
        confirm_email = self.cleaned_data.get('confirm_email')
        email = self.cleaned_data.get('email')
        
        if confirm_email and email and confirm_email != email:
            raise ValidationError(
                get_error_message('email', 'email_mismatch'),
                code='email_mismatch'
            )
        
        return confirm_email


class PasswordValidationMixin:
    """
    Mixin for password field validation with strength checking.
    
    Provides methods for validating password strength, confirmation matching,
    and common password-related validation patterns.
    """
    
    def clean_password1(self):
        """
        Clean and validate first password field.
        
        Returns:
            str: Cleaned password
            
        Raises:
            ValidationError: If password doesn't meet requirements
        """
        password1 = self.cleaned_data.get('password1')
        
        if not password1:
            return password1
            
        # Check maximum length
        if len(password1) > 128:
            raise ValidationError(
                get_error_message('password', 'password_too_long'),
                code='password_too_long'
            )
        
        # Use Django's built-in password validation
        user = self._get_user_for_password_validation()
        try:
            validate_password(password1, user)
        except ValidationError as error:
            # Transform Django's error messages to our custom ones
            transformed_errors = []
            for message in error.messages:
                if 'at least 12 characters' in message or 'at least 8 characters' in message:
                    transformed_errors.append(get_error_message('password', 'too_short'))
                elif 'common' in message:
                    transformed_errors.append(get_error_message('password', 'too_common'))
                elif 'similar' in message:
                    transformed_errors.append(get_error_message('password', 'too_similar'))
                elif 'numeric' in message:
                    transformed_errors.append(get_error_message('password', 'entirely_numeric'))
                else:
                    transformed_errors.append(str(message))
            
            raise ValidationError(transformed_errors)
        
        # No additional custom strength validation beyond Django's validators
        # (which already enforce a minimum length of 12 characters).
        
        return password1
    
    def clean_password2(self):
        """
        Clean and validate password confirmation field.
        
        Returns:
            str: Cleaned password confirmation
            
        Raises:
            ValidationError: If passwords don't match
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        
        if password1 and password2 and password1 != password2:
            raise ValidationError(
                get_error_message('password', 'password_mismatch'),
                code='password_mismatch'
            )
        
        return password2
    
    def _get_user_for_password_validation(self):
        """
        Get user instance for password validation context.
        
        Returns:
            CustomUser: User instance or new user with email
        """
        if hasattr(self, 'instance') and self.instance:
            return self.instance
        
        # Create temporary user for validation
        email = self.cleaned_data.get('email')
        return CustomUser(email=email) if email else CustomUser()
    
    def _check_password_strength(self, password):
        """
        Check password strength with custom rules.
        
        Args:
            password (str): Password to check
            
        Returns:
            bool: True if password meets strength requirements
        """
        if len(password) < 8:
            return False
            
        # Check for required character types
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        
        return has_upper and has_lower and has_digit and has_special


class PhoneValidationMixin:
    """
    Mixin for phone number field validation and normalization.
    
    Provides methods for validating phone numbers, normalizing formats,
    and handling international/US phone number patterns.
    """
    
    def clean_phone_number(self):
        """
        Clean and validate phone number field.
        
        Returns:
            str: Cleaned and normalized phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        phone = self.cleaned_data.get('phone_number')
        return self._clean_phone_field(phone)
    
    def clean_phone(self):
        """
        Clean and validate phone field (alternative name).
        
        Returns:
            str: Cleaned and normalized phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        phone = self.cleaned_data.get('phone')
        return self._clean_phone_field(phone)
    
    def _clean_phone_field(self, phone):
        """
        Internal method to clean phone number field.
        
        Args:
            phone (str): Raw phone number input
            
        Returns:
            str: Cleaned phone number
            
        Raises:
            ValidationError: If phone number is invalid
        """
        # Check if field is required and empty
        if not phone and hasattr(self, 'fields'):
            # Check if this is a phone field that should be required
            phone_fields = ['phone', 'phone_number']
            for field_name in phone_fields:
                if field_name in self.fields and self.fields[field_name].required:
                    raise ValidationError(
                        get_error_message('phone', 'phone_required'),
                        code='required'
                    )
        
        if not phone:
            return phone
            
        # Remove common formatting characters
        original_phone = phone
        phone = phone.strip()
        
        # Check for invalid characters
        if not re.match(r'^[\d\s\-\(\)\+\.]+$', phone):
            raise ValidationError(
                get_error_message('phone', 'invalid_characters'),
                code='invalid_characters'
            )
        
        # Extract only digits
        digits_only = re.sub(r'[^\d]', '', phone)
        
        # Validate length
        if len(digits_only) < 10:
            raise ValidationError(
                get_error_message('phone', 'too_short'),
                code='too_short'
            )
        
        if len(digits_only) > 15:
            raise ValidationError(
                get_error_message('phone', 'too_long'),
                code='too_long'
            )
        
        # Normalize format
        normalized_phone = self._normalize_phone_number(original_phone, digits_only)
        
        return normalized_phone
    
    def _normalize_phone_number(self, original_phone, digits_only):
        """
        Normalize phone number to consistent format.
        
        Args:
            original_phone (str): Original phone input
            digits_only (str): Phone number with only digits
            
        Returns:
            str: Normalized phone number
        """
        # Handle international format
        if original_phone.startswith('+'):
            # For US numbers with +1 prefix, format nicely
            if len(digits_only) == 11 and digits_only.startswith('1'):
                # Format as (XXX) XXX-XXXX
                area_code = digits_only[1:4]
                exchange = digits_only[4:7]
                number = digits_only[7:11]
                return f"({area_code}) {exchange}-{number}"
            else:
                # For other international numbers, keep the + prefix
                return '+' + digits_only
        
        # Handle US format
        if len(digits_only) == 10:
            # Format as (XXX) XXX-XXXX
            area_code = digits_only[:3]
            exchange = digits_only[3:6]
            number = digits_only[6:10]
            return f"({area_code}) {exchange}-{number}"
        elif len(digits_only) == 11 and digits_only.startswith('1'):
            # Remove country code and format as (XXX) XXX-XXXX
            area_code = digits_only[1:4]
            exchange = digits_only[4:7]
            number = digits_only[7:11]
            return f"({area_code}) {exchange}-{number}"
        
        # Return as-is for other formats
        return original_phone


class ImageValidationMixin:
    """
    Mixin for image field validation with size and format checking.
    
    Provides methods for validating image uploads, checking file sizes,
    formats, and dimensions. Enhanced with image processing capabilities.
    """
    
    def clean_profile_picture(self):
        """
        Clean and validate profile picture field.
        
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        image = self.cleaned_data.get('profile_picture')
        return self._clean_image_field(image, 'profile_picture')
    
    def clean_logo(self):
        """
        Clean and validate logo field.
        
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        image = self.cleaned_data.get('logo')
        return self._clean_image_field(image, 'logo')
    
    def _clean_image_field(self, image, field_name='image'):
        """
        Internal method to clean image field with enhanced validation and processing.
        
        Args:
            image: Image file object
            field_name (str): Name of the field being validated
            
        Returns:
            File: Cleaned and processed image file
            
        Raises:
            ValidationError: If image is invalid
        """
        if not image:
            return image
            
        # Skip validation for existing images
        if hasattr(image, 'url') and not hasattr(image, 'content_type'):
            return image
        
        # Use enhanced image validation from utils
        try:
            from .utils.helpers import validate_image_file, process_profile_image
            
            # Validate the image file
            validate_image_file(image)
            
            # Process the image if it's a new upload
            if hasattr(image, 'content_type') and image.content_type:
                # Only process new uploads, not existing images
                processed_image = process_profile_image(image)
                if processed_image:
                    return processed_image
            
        except ImportError:
            # Fall back to basic validation if utils are not available
            pass
        except Exception as e:
            # If processing fails, fall back to basic validation
            import logging
            logging.warning(f"Image processing failed: {str(e)}")
        
        # Fallback to basic validation
        # Check file size (5MB limit)
        max_size = 5 * 1024 * 1024
        if hasattr(image, 'size') and image.size > max_size:
            raise ValidationError(
                get_error_message('image', 'file_too_large', max_size=5),
                code='file_too_large'
            )
        
        # Check file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
        if hasattr(image, 'content_type') and image.content_type not in allowed_types:
            raise ValidationError(
                get_error_message('image', 'invalid_format'),
                code='invalid_format'
            )
        
        # Check file extension
        if hasattr(image, 'name'):
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
            file_extension = '.' + image.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                raise ValidationError(
                    get_error_message('image', 'invalid_extension'),
                    code='invalid_extension'
                )
        
        # Validate image dimensions
        self._validate_image_dimensions(image, field_name)
        
        return image
    
    def _validate_image_dimensions(self, image, field_name):
        """
        Validate image dimensions.
        
        Args:
            image: Image file object
            field_name (str): Name of the field being validated
            
        Raises:
            ValidationError: If image dimensions are invalid
        """
        try:
            from PIL import Image
            
            # Reset file pointer
            image.seek(0)
            
            # Open and verify image
            pil_image = Image.open(image)
            pil_image.verify()
            
            # Reset file pointer after verification
            image.seek(0)
            
            # Check dimensions
            pil_image = Image.open(image)
            width, height = pil_image.size
            
            # Set dimension limits based on field type
            if field_name == 'profile_picture':
                min_width = min_height = 100
                max_width = max_height = 1000
            elif field_name == 'logo':
                min_width = min_height = 50
                max_width = max_height = 2000
            else:
                min_width = min_height = 50
                max_width = max_height = 2000
            
            if width < min_width or height < min_height:
                raise ValidationError(
                    get_error_message('image', 'image_too_small', 
                                    min_width=min_width, min_height=min_height),
                    code='image_too_small'
                )
            
            if width > max_width or height > max_height:
                raise ValidationError(
                    get_error_message('image', 'image_too_large',
                                    max_width=max_width, max_height=max_height),
                    code='image_too_large'
                )
            
            # Reset file pointer for saving
            image.seek(0)
            
        except ImportError:
            # PIL not available, skip dimension validation
            pass
        except Exception:
            raise ValidationError(
                get_error_message('image', 'invalid_image'),
                code='invalid_image'
            )


class URLValidationMixin:
    """
    Mixin for URL field validation with format checking.
    
    Provides methods for validating website URLs, social media URLs,
    and other URL-related fields.
    """
    
    def clean_website(self):
        """
        Clean and validate website URL field.
        
        Returns:
            str: Cleaned website URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        website = self.cleaned_data.get('website')
        return self._clean_url_field(website, 'website')
    
    def clean_instagram(self):
        """
        Clean and validate Instagram URL field.
        
        Returns:
            str: Cleaned Instagram URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        instagram = self.cleaned_data.get('instagram')
        return self._clean_social_url_field(instagram, 'instagram')
    
    def clean_facebook(self):
        """
        Clean and validate Facebook URL field.
        
        Returns:
            str: Cleaned Facebook URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        facebook = self.cleaned_data.get('facebook')
        return self._clean_social_url_field(facebook, 'facebook')
    
    def _clean_url_field(self, url, field_name='url'):
        """
        Internal method to clean URL field.
        
        Args:
            url (str): Raw URL input
            field_name (str): Name of the field being validated
            
        Returns:
            str: Cleaned URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            return url
            
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Check URL length
        if len(url) > 2000:
            raise ValidationError(
                get_error_message('url', 'url_too_long'),
                code='url_too_long'
            )
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            raise ValidationError(
                get_error_message('url', 'invalid_format'),
                code='invalid_format'
            )
        
        return url
    
    def _clean_social_url_field(self, url, platform):
        """
        Clean social media URL field.
        
        Args:
            url (str): Raw URL input
            platform (str): Social media platform name
            
        Returns:
            str: Cleaned social media URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            return url
            
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Platform-specific validation
        if platform == 'instagram':
            if not re.match(r'^https?://(www\.)?instagram\.com/', url, re.IGNORECASE):
                raise ValidationError(
                    get_error_message('url', 'social_media_invalid', platform='Instagram'),
                    code='social_media_invalid'
                )
        elif platform == 'facebook':
            if not re.match(r'^https?://(www\.)?facebook\.com/', url, re.IGNORECASE):
                raise ValidationError(
                    get_error_message('url', 'social_media_invalid', platform='Facebook'),
                    code='social_media_invalid'
                )
        
        return url


class BusinessValidationMixin:
    """
    Mixin for business-related field validation.
    
    Provides methods for validating business names, EIN numbers,
    addresses, and other business-specific fields.
    """
    
    def clean_ein(self):
        """
        Clean and validate EIN (Employer Identification Number) field.
        
        Returns:
            str: Cleaned EIN
            
        Raises:
            ValidationError: If EIN is invalid
        """
        ein = self.cleaned_data.get('ein')
        
        if not ein:
            return ein
            
        # Remove formatting
        ein = re.sub(r'[^\d]', '', ein)
        
        # Check length
        if len(ein) != 9:
            raise ValidationError(
                get_error_message('business', 'invalid_ein'),
                code='invalid_ein'
            )
        
        # Format EIN
        formatted_ein = f"{ein[:2]}-{ein[2:]}"
        
        return formatted_ein
    
    def clean_zip_code(self):
        """
        Clean and validate ZIP code field.
        
        Returns:
            str: Cleaned ZIP code
            
        Raises:
            ValidationError: If ZIP code is invalid
        """
        zip_code = self.cleaned_data.get('zip_code')
        
        # Check if field is required and empty
        if not zip_code and hasattr(self, 'fields') and 'zip_code' in self.fields and self.fields['zip_code'].required:
            raise ValidationError(
                get_error_message('text', 'required_field'),
                code='required'
            )
        
        if not zip_code:
            return zip_code
            
        # Remove formatting
        zip_code = re.sub(r'[^\d]', '', zip_code)
        
        # Check format (5 digits or 5+4 digits)
        if not re.match(r'^\d{5}(\d{4})?$', zip_code):
            raise ValidationError(
                get_error_message('business', 'invalid_zip_code'),
                code='invalid_zip_code'
            )
        
        return zip_code
    
    def clean_legal_name(self):
        """
        Clean and validate legal business name field.
        
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        legal_name = self.cleaned_data.get('legal_name')
        return self._clean_business_name_field(legal_name, 'legal_name')
    
    def clean_business_name(self):
        """
        Clean and validate business name field.
        
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        business_name = self.cleaned_data.get('business_name')
        return self._clean_business_name_field(business_name, 'business_name')
    
    def _clean_business_name_field(self, name, field_name):
        """
        Internal method to clean business name field.
        
        Args:
            name (str): Raw business name input
            field_name (str): Name of the field being validated
            
        Returns:
            str: Cleaned business name
            
        Raises:
            ValidationError: If business name is invalid
        """
        # Check if field is required and empty
        if not name and hasattr(self, 'fields') and field_name in self.fields and self.fields[field_name].required:
            raise ValidationError(
                get_error_message('text', 'required_field'),
                code='required'
            )
        
        if not name:
            return name
            
        name = name.strip()
        
        # Check length
        if len(name) < 2:
            raise ValidationError(
                get_error_message('text', 'too_short', min_length=2),
                code='too_short'
            )
        
        if len(name) > 200:
            raise ValidationError(
                get_error_message('text', 'too_long', max_length=200),
                code='too_long'
            )
        
        # Check for valid characters (allow letters, numbers, spaces, and common business punctuation)
        if not re.match(r'^[a-zA-Z0-9\s\-\'\.\&\,\(\)]+$', name):
            raise ValidationError(
                get_error_message('text', 'invalid_business_name'),
                code='invalid_business_name'
            )
        
        return name


class TermsValidationMixin:
    """
    Mixin for terms and conditions validation.
    
    Provides methods for validating agreement checkboxes and
    consent fields.
    """
    
    def clean_agree_to_terms(self):
        """
        Clean and validate terms agreement field.
        
        Returns:
            bool: Cleaned terms agreement value
            
        Raises:
            ValidationError: If terms not agreed to
        """
        agree_to_terms = self.cleaned_data.get('agree_to_terms')
        
        if not agree_to_terms:
            raise ValidationError(
                get_error_message('terms', 'terms_required'),
                code='terms_required'
            )
        
        return agree_to_terms
    
    def clean_privacy_policy(self):
        """
        Clean and validate privacy policy agreement field.
        
        Returns:
            bool: Cleaned privacy agreement value
            
        Raises:
            ValidationError: If privacy policy not agreed to
        """
        privacy_policy = self.cleaned_data.get('privacy_policy')
        
        if not privacy_policy:
            raise ValidationError(
                get_error_message('terms', 'privacy_required'),
                code='privacy_required'
            )
        
        return privacy_policy 