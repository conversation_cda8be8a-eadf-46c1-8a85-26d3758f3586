# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.views import (
    PasswordResetCompleteView,
    PasswordResetConfirmView,
    PasswordResetDoneView,
    PasswordResetView,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, FormView, UpdateView
from django.utils.translation import gettext_lazy as _
import logging

# --- Local App Imports ---
from ..decorators import ServiceProviderRequiredMixin, AnonymousRequiredMixin, service_provider_required
from ..forms import (
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
    ServiceProviderPasswordChangeForm,
    TeamMemberForm,
    AccountDeactivationForm,
)
from ..utils.logging import (
    log_account_lifecycle_event,
    log_authentication_event,
    log_error,
    log_user_activity,
    performance_monitor,
)
from ..models import CustomUser, ServiceProviderProfile, TeamMember
from ..services import (
    AuthenticationService,
    UserRegistrationService,
    ProfileService,
    PasswordService,
    AccountService,
    EmailVerificationService,
    TeamService,
    AuthenticationError,
    ProfileError,
    SecurityError,
)
from .common import MESSAGES, logger, record_login_attempt, get_client_ip

# Provider-specific logger
provider_logger = logging.getLogger('accounts_app.provider')


# --- Service Provider Authentication and Account Management ---

class ServiceProviderSignupView(AnonymousRequiredMixin, CreateView):
    """
    Service provider registration with email verification workflow using UserRegistrationService.
    """
    model = CustomUser
    form_class = ServiceProviderSignupForm
    template_name = 'accounts_app/provider/signup.html'
    success_url = reverse_lazy('accounts_app:provider_signup_done')
    
    def form_valid(self, form):
        """Process valid signup form submissions using UserRegistrationService"""
        try:
            # Prepare user data for service
            user_data = {
                'email': form.cleaned_data['email'],
                'password': form.cleaned_data['password1'],
                'first_name': form.cleaned_data.get('first_name', ''),
                'last_name': form.cleaned_data.get('last_name', ''),
                'business_name': form.cleaned_data['business_name'],
                'contact_person_name': form.cleaned_data['contact_person_name'],
                'ein': form.cleaned_data.get('ein', ''),
            }
            
            # Use service to register provider
            success, user, message = UserRegistrationService.register_service_provider(
                user_data=user_data,
                request=self.request
            )
            
            if success and user:
                self.object = user
                messages.success(self.request, MESSAGES['provider_signup'])
                provider_logger.info(f"Service provider registration successful: {user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message or _("Registration failed. Please try again."))
                provider_logger.error(f"Service provider registration failed: {message}")
                return self.form_invalid(form)
                
        except Exception as error:
            log_error(
                error_type='provider_signup',
                error_message="Unexpected error during service provider signup",
                request=self.request,
                exception=error,
                details={'form_data': form.cleaned_data}
            )
            messages.error(self.request, _("Registration failed. Please try again."))
            provider_logger.error(f"Service provider signup error: {str(error)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider signup form validation failed",
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider signup form validation failed: {form.errors}")
        
        return super().form_invalid(form)


@require_http_methods(["GET"])
@performance_monitor("provider_signup_done")
def provider_signup_done_view(request):
    """
    Display signup completion page after successful registration with error handling.
    """
    try:
        return render(request, 'accounts_app/provider/signup_done.html')
    except Exception as e:
        log_error(
            error_type='template_render',
            error_message="Error rendering provider signup done page",
            request=request,
            exception=e
        )
        provider_logger.error(f"Provider signup done page error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/signup_done.html')


@require_http_methods(["GET"])
@performance_monitor("provider_email_verification")
def provider_email_verify_view(request, uidb64, token):
    """
    Handle email verification for service provider accounts using AccountService.
    """
    try:
        from django.contrib.auth.tokens import default_token_generator
        from django.utils.http import urlsafe_base64_decode
        from django.utils.encoding import force_str
        
        # Decode user ID
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
            user = None
        
        if user is not None and default_token_generator.check_token(user, token):
            # Use service to verify email
            success, message = AccountService.verify_email(user, request)
            
            if success:
                messages.success(request, MESSAGES['email_verified'])
                provider_logger.info(f"Service provider email verified: {user.email}")
                return redirect('accounts_app:provider_login')
            else:
                messages.error(request, message)
                provider_logger.error(f"Service provider email verification failed: {user.email}")
                return render(request, 'accounts_app/provider/email_verification_failed.html')
        else:
            messages.error(request, MESSAGES['verification_link_invalid'])
            provider_logger.warning(f"Invalid verification link accessed: {uidb64}")
            return render(request, 'accounts_app/provider/email_verification_failed.html')
            
    except Exception as e:
        log_error(
            error_type='email_verification',
            error_message="Unexpected error during provider email verification",
            request=request,
            exception=e,
            details={'uidb64': uidb64, 'token': token}
        )
        messages.error(request, _("Verification failed. Please try again."))
        provider_logger.error(f"Provider email verification error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/email_verification_failed.html')


class ServiceProviderProfileView(ServiceProviderRequiredMixin, DetailView):
    """
    Display service provider profile with comprehensive error handling.
    """
    model = ServiceProviderProfile
    template_name = 'accounts_app/provider/profile.html'
    context_object_name = 'profile'

    def get_object(self, queryset=None):
        """Get or create service provider profile with error handling"""
        try:
            profile, created = ServiceProviderProfile.objects.get_or_create(user=self.request.user)
            if created:
                provider_logger.info(f"Service provider profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving service provider profile",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            provider_logger.error(f"Service provider profile retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object to prevent template errors
            return ServiceProviderProfile(user=self.request.user)


class ServiceProviderProfileEditView(ServiceProviderRequiredMixin, UpdateView):
    """
    Handle service provider profile updates using ProfileService.
    """
    model = ServiceProviderProfile
    form_class = ServiceProviderProfileForm
    template_name = 'accounts_app/provider/profile_edit.html'
    success_url = reverse_lazy('accounts_app:service_provider_profile')

    def get_object(self, queryset=None):
        """Get or create service provider profile with error handling"""
        try:
            profile, created = ServiceProviderProfile.objects.get_or_create(user=self.request.user)
            if created:
                provider_logger.info(f"Service provider profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving service provider profile for edit",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            provider_logger.error(f"Service provider profile edit retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object
            return ServiceProviderProfile(user=self.request.user)

    def form_valid(self, form):
        """Handle valid form submission using ProfileService"""
        try:
            # Prepare profile data for service
            profile_data = form.cleaned_data
            
            # Use service to update profile
            success, message = ProfileService.update_service_provider_profile(
                user=self.request.user,
                profile_data=profile_data,
                request=self.request
            )
            
            if success:
                messages.success(self.request, MESSAGES['business_profile_update'])
                provider_logger.info(f"Service provider profile updated successfully: {self.request.user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message)
                provider_logger.error(f"Service provider profile update failed: {self.request.user.email}")
                return self.form_invalid(form)
                
        except ProfileError as e:
            messages.error(self.request, str(e))
            provider_logger.error(f"Service provider profile update error: {str(e)}")
            return self.form_invalid(form)
        except Exception as e:
            log_error(
                error_type='profile_update',
                error_message="Unexpected error during service provider profile update",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            messages.error(self.request, _("Profile update failed. Please try again."))
            provider_logger.error(f"Service provider profile update error: {str(e)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider profile form validation failed",
                user=self.request.user,
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider profile form validation failed: {form.errors}")
        
        return super().form_invalid(form)


# --- Service Provider Security Features ---

@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_change_password_view(request):
    """
    Change password for service provider using PasswordService.
    """
    try:
        if request.method == 'POST':
            from ..forms import ServiceProviderPasswordChangeForm
            form = ServiceProviderPasswordChangeForm(user=request.user, data=request.POST)
            
            if form.is_valid():
                old_password = form.cleaned_data['old_password']
                new_password = form.cleaned_data['new_password1']
                
                # Use service to change password
                success, message = PasswordService.change_password(
                    user=request.user,
                    old_password=old_password,
                    new_password=new_password,
                    request=request
                )
                
                if success:
                    # Logout user for security
                    AuthenticationService.logout_user(request)
                    messages.success(request, MESSAGES['password_change'])
                    provider_logger.info(f"Service provider password changed successfully: {request.user.email}")
                    return redirect('home')
                else:
                    messages.error(request, message)
                    provider_logger.error(f"Service provider password change failed: {request.user.email}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Service provider password change form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    provider_logger.warning(f"Service provider password change form validation failed: {form.errors}")
        else:
            from ..forms import ServiceProviderPasswordChangeForm
            form = ServiceProviderPasswordChangeForm(user=request.user)
        
        return render(request, 'accounts_app/provider/change_password.html', {'form': form})
        
    except Exception as e:
        log_error(
            error_type='password_change',
            error_message="Unexpected error during service provider password change",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, MESSAGES['password_change_error'])
        provider_logger.error(f"Service provider password change error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/change_password.html', {'form': ServiceProviderPasswordChangeForm(user=request.user)})


@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_deactivate_account_view(request):
    """
    Deactivate service provider account using AccountService.
    """
    try:
        if request.method == 'POST':
            form = AccountDeactivationForm(user=request.user, data=request.POST)
            
            if form.is_valid():
                reason = form.cleaned_data.get('reason', 'Service provider requested deactivation')
                
                # Use service to deactivate account
                success, message = AccountService.deactivate_account(
                    user=request.user,
                    reason=reason,
                    request=request
                )
                
                if success:
                    # Logout user
                    AuthenticationService.logout_user(request)
                    messages.success(request, MESSAGES['account_deactivated'])
                    provider_logger.info(f"Service provider account deactivated: {request.user.email}")
                    return redirect('home')
                else:
                    messages.error(request, message)
                    provider_logger.error(f"Service provider account deactivation failed: {request.user.email}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Service provider account deactivation form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    provider_logger.warning(f"Service provider account deactivation form validation failed: {form.errors}")
        else:
            form = AccountDeactivationForm(user=request.user)
        
        return render(request, 'accounts_app/provider/deactivate_account.html', {'form': form})
        
    except Exception as e:
        log_error(
            error_type='account_deactivation',
            error_message="Unexpected error during service provider account deactivation",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, MESSAGES['deactivation_error'])
        provider_logger.error(f"Service provider account deactivation error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/deactivate_account.html', {'form': AccountDeactivationForm(user=request.user)})


# --- Service Provider Password Reset (Class-based) ---

class ServiceProviderPasswordResetView(PasswordResetView):
    """
    Initiate password reset for service providers with comprehensive error handling.
    """
    template_name = 'accounts_app/provider/password_reset.html'
    email_template_name = 'accounts_app/provider/password_reset_email.html'
    subject_template_name = 'accounts_app/provider/password_reset_subject.txt'
    success_url = reverse_lazy('accounts_app:service_provider_password_reset_done')

    def get_form(self, form_class=None):
        """Apply form styling with error handling"""
        try:
            form = super().get_form(form_class)
            form.fields['email'].widget.attrs.update({
                'class': 'form-control',
                'placeholder': 'Enter your business email address',
            })
            return form
        except Exception as e:
            provider_logger.error(f"Error creating service provider password reset form: {str(e)}", exc_info=True)
            return super().get_form(form_class)

    def get_initial(self):
        """Pre-populate email if provided via GET parameter with error handling"""
        try:
            initial = super().get_initial()
            email = self.request.GET.get('email', '')
            if email:
                initial['email'] = email
            return initial
        except Exception as e:
            provider_logger.error(f"Error setting initial service provider password reset data: {str(e)}", exc_info=True)
            return super().get_initial()

    def form_valid(self, form):
        """Store email in session for the done view with error handling"""
        try:
            self.request.session['password_reset_email'] = form.cleaned_data['email']
            provider_logger.info(f"Service provider password reset initiated for: {form.cleaned_data['email']}")
            return super().form_valid(form)
        except Exception as e:
            log_error(
                error_type='password_reset',
                error_message="Error processing service provider password reset request",
                request=self.request,
                exception=e,
                details={'email': form.cleaned_data.get('email', '')}
            )
            provider_logger.error(f"Service provider password reset error: {str(e)}", exc_info=True)
            return super().form_valid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider password reset form validation failed",
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider password reset form validation failed: {form.errors}")
        
        return super().form_invalid(form)

    def get(self, request, *args, **kwargs):
        """Handle GET request with error handling"""
        try:
            response = super().get(request, *args, **kwargs)
            
            # Ensure context is available for tests
            if hasattr(response, 'render') and callable(response.render):
                try:
                    response = response.render()
                except Exception as e:
                    provider_logger.error(f"Error rendering service provider password reset form: {str(e)}")
            
            if getattr(response, 'context', None) is None:
                ctx = getattr(response, 'context_data', {})
                response.context = ctx
                try:
                    response._context = ctx
                except Exception:
                    pass
            
            return response
        except Exception as e:
            log_error(
                error_type='password_reset_get',
                error_message="Error handling service provider password reset GET request",
                request=request,
                exception=e
            )
            provider_logger.error(f"Service provider password reset GET error: {str(e)}", exc_info=True)
            return super().get(request, *args, **kwargs)


class ServiceProviderPasswordResetDoneView(PasswordResetDoneView):
    """
    Display password reset done confirmation with error handling.
    """
    template_name = 'accounts_app/provider/password_reset_done.html'

    def get_context_data(self, **kwargs):
        """Add email to context with error handling"""
        try:
            context = super().get_context_data(**kwargs)
            email = self.request.session.pop('password_reset_email', None)
            if email:
                context['reset_email'] = email
            return context
        except Exception as e:
            provider_logger.error(f"Error getting service provider password reset done context: {str(e)}", exc_info=True)
            return super().get_context_data(**kwargs)


class ServiceProviderPasswordResetConfirmView(PasswordResetConfirmView):
    """
    Confirm new password entry for service providers with comprehensive error handling.
    """
    template_name = 'accounts_app/provider/password_reset_confirm.html'
    success_url = reverse_lazy('accounts_app:service_provider_password_reset_complete')

    def get_form(self, form_class=None):
        """Apply form styling with error handling"""
        try:
            form = super().get_form(form_class)
            for field in form.fields.values():
                field.widget.attrs.update({'class': 'form-control'})
            return form
        except Exception as e:
            provider_logger.error(f"Error creating service provider password reset confirm form: {str(e)}", exc_info=True)
            return super().get_form(form_class)

    def form_valid(self, form):
        """Handle valid password reset confirmation using PasswordService"""
        try:
            # Get the user from the form
            user = form.user
            new_password = form.cleaned_data['new_password1']
            
            # Use service to reset password
            success, message = PasswordService.reset_password(
                user=user,
                new_password=new_password,
                request=self.request
            )
            
            if success:
                provider_logger.info(f"Service provider password reset completed for: {user.email}")
                return super().form_valid(form)
            else:
                provider_logger.error(f"Service provider password reset service failed for: {user.email}")
                return super().form_valid(form)  # Continue with default behavior
                
        except Exception as e:
            log_error(
                error_type='password_reset_confirm',
                error_message="Error during service provider password reset confirmation",
                request=self.request,
                exception=e
            )
            provider_logger.error(f"Service provider password reset confirm error: {str(e)}", exc_info=True)
            return super().form_valid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider password reset confirm form validation failed",
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider password reset confirm form validation failed: {form.errors}")
        
        return super().form_invalid(form)


class ServiceProviderPasswordResetCompleteView(PasswordResetCompleteView):
    """
    Display confirmation of successful password reset with error handling.
    """
    template_name = 'accounts_app/provider/password_reset_complete.html'

    def get_context_data(self, **kwargs):
        """Add context with error handling"""
        try:
            context = super().get_context_data(**kwargs)
            context['login_url'] = reverse_lazy('accounts_app:provider_login')
            return context
        except Exception as e:
            provider_logger.error(f"Error getting service provider password reset complete context: {str(e)}", exc_info=True)
            return super().get_context_data(**kwargs)


# --- Team Management ---

@service_provider_required
@require_http_methods(["GET"])
def service_provider_team_list_view(request):
    """
    Display team members list with error handling.
    """
    try:
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_members = TeamMember.objects.filter(service_provider=profile).order_by('name')
        
        context = {
            'team_members': team_members,
            'profile': profile,
            'max_team_members': TeamMember.max_count(),
            'current_count': team_members.count(),
        }
        
        return render(request, 'accounts_app/provider/team_list.html', context)
        
    except ServiceProviderProfile.DoesNotExist:
        log_error(
            error_type='profile_not_found',
            error_message="Service provider profile not found for team list",
            user=request.user,
            request=request
        )
        messages.error(request, _("Profile not found. Please contact support."))
        provider_logger.error(f"Service provider profile not found for team list: {request.user.email}")
        return redirect('accounts_app:service_provider_profile')
    except Exception as e:
        log_error(
            error_type='team_list',
            error_message="Error displaying team members list",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, _("Error loading team members. Please try again."))
        provider_logger.error(f"Service provider team list error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_profile')


@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_team_add_view(request):
    """
    Add team member using TeamService.
    """
    try:
        profile = ServiceProviderProfile.objects.get(user=request.user)
        
        if request.method == 'POST':
            form = TeamMemberForm(data=request.POST, files=request.FILES)
            
            if form.is_valid():
                member_data = {
                    'name': form.cleaned_data['name'],
                    'position': form.cleaned_data['position'],
                    'photo': form.cleaned_data.get('photo'),
                    'is_active': form.cleaned_data.get('is_active', True),
                }
                
                # Use service to add team member
                success, team_member, message = TeamService.add_team_member(
                    service_provider=profile,
                    member_data=member_data,
                    request=request
                )
                
                if success:
                    messages.success(request, _("Team member added successfully"))
                    provider_logger.info(f"Team member added: {member_data['name']} to {profile.legal_name}")
                    return redirect('accounts_app:service_provider_team_list')
                else:
                    messages.error(request, message)
                    provider_logger.error(f"Team member addition failed: {message}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Team member form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    provider_logger.warning(f"Team member form validation failed: {form.errors}")
        else:
            form = TeamMemberForm()
        
        context = {
            'form': form,
            'profile': profile,
            'max_team_members': TeamMember.max_count(),
            'current_count': TeamMember.objects.filter(service_provider=profile).count(),
        }
        
        return render(request, 'accounts_app/provider/team_member_form.html', context)
        
    except ServiceProviderProfile.DoesNotExist:
        log_error(
            error_type='profile_not_found',
            error_message="Service provider profile not found for team member addition",
            user=request.user,
            request=request
        )
        messages.error(request, _("Profile not found. Please contact support."))
        provider_logger.error(f"Service provider profile not found for team member addition: {request.user.email}")
        return redirect('accounts_app:service_provider_profile')
    except Exception as e:
        log_error(
            error_type='team_member_add',
            error_message="Unexpected error during team member addition",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, _("Error adding team member. Please try again."))
        provider_logger.error(f"Team member addition error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_team_edit_view(request, team_member_id):
    """
    Edit team member using TeamService.
    """
    try:
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        if request.method == 'POST':
            form = TeamMemberForm(data=request.POST, files=request.FILES, instance=team_member)
            
            if form.is_valid():
                member_data = {
                    'name': form.cleaned_data['name'],
                    'position': form.cleaned_data['position'],
                    'photo': form.cleaned_data.get('photo'),
                    'is_active': form.cleaned_data.get('is_active', True),
                }
                
                # Use service to update team member
                success, message = TeamService.update_team_member(
                    team_member=team_member,
                    member_data=member_data,
                    request=request
                )
                
                if success:
                    messages.success(request, _("Team member updated successfully"))
                    provider_logger.info(f"Team member updated: {team_member.name}")
                    return redirect('accounts_app:service_provider_team_list')
                else:
                    messages.error(request, message)
                    provider_logger.error(f"Team member update failed: {message}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Team member edit form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    provider_logger.warning(f"Team member edit form validation failed: {form.errors}")
        else:
            form = TeamMemberForm(instance=team_member)
        
        context = {
            'form': form,
            'team_member': team_member,
            'profile': profile,
        }
        
        return render(request, 'accounts_app/provider/team_member_form.html', context)
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        provider_logger.error(f"Team member not found: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_edit',
            error_message="Unexpected error during team member edit",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error editing team member. Please try again."))
        provider_logger.error(f"Team member edit error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["POST"])
def service_provider_team_delete_view(request, team_member_id):
    """
    Delete team member using TeamService.
    """
    try:
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        # Use service to remove team member
        success, message = TeamService.remove_team_member(
            team_member=team_member,
            request=request
        )
        
        if success:
            messages.success(request, _("Team member removed successfully"))
            provider_logger.info(f"Team member removed: {team_member.name}")
        else:
            messages.error(request, message)
            provider_logger.error(f"Team member removal failed: {message}")
        
        return redirect('accounts_app:service_provider_team_list')
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        provider_logger.error(f"Team member not found for deletion: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_delete',
            error_message="Unexpected error during team member deletion",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error removing team member. Please try again."))
        provider_logger.error(f"Team member deletion error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')